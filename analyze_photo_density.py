#!/usr/bin/env python3
"""
分析照片密度，找出700+张照片的具体原因
"""

import sys
import os
from datetime import datetime, timezone, timedelta
from collections import defaultdict

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

from photo_dedup.collector import collect_photos_with_smart_cache_by_date_range

def analyze_high_density_periods(library_path, days_to_analyze=90):
    """分析高密度照片时间段"""
    
    print(f"🔍 分析最近 {days_to_analyze} 天的照片密度分布")
    print(f"📂 Photos库: {library_path}")
    print("=" * 100)
    
    current_date = datetime.now(tz=timezone.utc)
    high_density_periods = []
    
    # 按5天为单位分析
    for days_ago in range(0, days_to_analyze, 5):
        search_end = current_date - timedelta(days=days_ago)
        search_start = search_end - timedelta(days=5)
        
        start_timestamp = int(search_start.timestamp())
        end_timestamp = int(search_end.timestamp())
        
        try:
            photos = collect_photos_with_smart_cache_by_date_range(
                library_path=library_path,
                start_date=start_timestamp,
                end_date=end_timestamp,
                max_photos=0
            )
            
            photo_count = len(photos)
            
            # 分类显示
            if photo_count > 700:
                icon = "🔥🔥🔥"
                category = "超高密度"
            elif photo_count > 500:
                icon = "🔥🔥"
                category = "高密度"
            elif photo_count > 200:
                icon = "🔥"
                category = "中高密度"
            elif photo_count > 50:
                icon = "📸"
                category = "中等密度"
            elif photo_count > 0:
                icon = "📷"
                category = "低密度"
            else:
                icon = "📭"
                category = "无照片"
            
            print(f"{icon} {search_start.strftime('%Y-%m-%d')} 到 {search_end.strftime('%Y-%m-%d')}: {photo_count:4d} 张 ({category})")
            
            # 记录高密度时段
            if photo_count > 500:
                high_density_periods.append({
                    'start': search_start,
                    'end': search_end,
                    'count': photo_count,
                    'photos': photos
                })
                
        except Exception as e:
            print(f"❌ 查询失败: {search_start.strftime('%Y-%m-%d')} 到 {search_end.strftime('%Y-%m-%d')} - {e}")
    
    return high_density_periods

def analyze_period_details(period_info):
    """详细分析高密度时段"""
    
    start_date = period_info['start']
    end_date = period_info['end']
    photos = period_info['photos']
    
    print(f"\n🔍 详细分析: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} ({len(photos)} 张照片)")
    print("=" * 80)
    
    # 按日期分组
    daily_counts = defaultdict(int)
    daily_photos = defaultdict(list)
    
    for photo in photos:
        if isinstance(photo, dict):
            date_taken = photo.get('date_taken')
            filename = photo.get('filename', 'Unknown')
        else:
            date_taken = photo.date_taken
            filename = photo.filename
        
        # 处理日期
        if isinstance(date_taken, str):
            try:
                photo_date = datetime.fromisoformat(date_taken.replace('Z', '+00:00'))
            except:
                photo_date = datetime.now(tz=timezone.utc)
        elif isinstance(date_taken, (int, float)):
            photo_date = datetime.fromtimestamp(date_taken, tz=timezone.utc)
        elif isinstance(date_taken, datetime):
            photo_date = date_taken
        else:
            photo_date = datetime.now(tz=timezone.utc)
        
        day_key = photo_date.strftime('%Y-%m-%d')
        daily_counts[day_key] += 1
        daily_photos[day_key].append(filename)
    
    # 显示每日分布
    print("📅 每日照片分布:")
    for day in sorted(daily_counts.keys()):
        count = daily_counts[day]
        icon = "🔥" if count > 100 else "📸" if count > 20 else "📷"
        print(f"  {icon} {day}: {count:3d} 张照片")
        
        # 显示部分文件名（如果数量很多）
        if count > 50:
            sample_files = daily_photos[day][:5]
            print(f"    示例文件: {', '.join(sample_files)}")
            if count > 5:
                print(f"    ... 还有 {count - 5} 张照片")
    
    # 分析可能的原因
    print(f"\n💡 可能的原因分析:")
    max_daily = max(daily_counts.values()) if daily_counts else 0
    total_days = len(daily_counts)
    avg_per_day = len(photos) / max(total_days, 1)
    
    print(f"📊 统计信息:")
    print(f"  - 总照片数: {len(photos)} 张")
    print(f"  - 涉及天数: {total_days} 天")
    print(f"  - 平均每天: {avg_per_day:.1f} 张")
    print(f"  - 最多一天: {max_daily} 张")
    
    if max_daily > 200:
        print(f"🔥 某天照片数量极高 ({max_daily} 张)，可能原因:")
        print(f"  - 连拍模式拍摄")
        print(f"  - 批量导入照片")
        print(f"  - 特殊活动/旅行")
    elif avg_per_day > 100:
        print(f"📸 平均每天照片较多 ({avg_per_day:.1f} 张)，可能原因:")
        print(f"  - 旅行期间")
        print(f"  - 特殊事件期间")
        print(f"  - 摄影爱好者日常拍摄")
    
    # 分析文件名模式
    analyze_filename_patterns(daily_photos)

def analyze_filename_patterns(daily_photos):
    """分析文件名模式"""
    
    print(f"\n📋 文件名模式分析:")
    
    all_filenames = []
    for day_files in daily_photos.values():
        all_filenames.extend(day_files)
    
    # 统计文件名前缀
    prefix_counts = defaultdict(int)
    for filename in all_filenames:
        if filename and len(filename) > 3:
            prefix = filename[:3].upper()
            prefix_counts[prefix] += 1
    
    print("📁 文件名前缀分布:")
    for prefix, count in sorted(prefix_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {prefix}*: {count} 张")
    
    # 检查是否有连拍模式
    img_count = sum(1 for f in all_filenames if f and ('IMG_' in f.upper() or 'DSC_' in f.upper()))
    if img_count > len(all_filenames) * 0.8:
        print("📸 大部分是标准相机拍摄的照片")
    
    # 检查是否有批量导入
    if len(set(f[:8] for f in all_filenames if f and len(f) > 8)) < len(all_filenames) * 0.1:
        print("📥 可能存在批量导入的照片")

def main():
    # 替换为您的实际Photos库路径
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"
    
    if not os.path.exists(library_path):
        print(f"❌ Photos库路径不存在: {library_path}")
        print("请修改脚本中的library_path变量")
        return
    
    print("🚀 开始分析照片密度分布")
    
    try:
        # 分析高密度时段
        high_density_periods = analyze_high_density_periods(library_path, days_to_analyze=60)
        
        if high_density_periods:
            print(f"\n🔥 发现 {len(high_density_periods)} 个高密度时段 (>500张照片):")
            
            for i, period in enumerate(high_density_periods, 1):
                print(f"\n{'='*20} 高密度时段 {i} {'='*20}")
                analyze_period_details(period)
        else:
            print("\n✅ 没有发现超过500张照片的5天时间段")
            print("💡 700+张照片可能来自:")
            print("  - 更长的时间范围查询")
            print("  - 缓存累积的结果")
            print("  - 不同查询条件的组合")
        
        print(f"\n{'='*80}")
        print("✅ 分析完成")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
