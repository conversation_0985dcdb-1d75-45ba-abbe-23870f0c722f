#!/usr/bin/env python3
"""
调试无限滚动加载问题的脚本
"""

import sys
import os
from datetime import datetime, timezone, timedelta

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

from photo_dedup.collector import collect_photos_with_smart_cache_by_date_range
from photo_dedup.database import initialize_database, get_cached_date_range, is_date_range_cached

def debug_date_range_queries():
    """调试日期范围查询"""

    # 替换为您的实际Photos库路径
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"

    print("🔍 调试无限滚动加载问题")
    print(f"📂 Photos库路径: {library_path}")
    print("=" * 80)

    # 模拟前端的查询序列
    current_date = datetime.now(tz=timezone.utc)
    days_back = 5  # 每次查询5天
    max_photos_per_batch = 100  # 每批最多100张

    print(f"📅 当前时间: {current_date}")
    print(f"🔢 每次查询天数: {days_back}")
    print(f"📸 每批最大照片数: {max_photos_per_batch}")
    print("=" * 80)

    # 初始化数据库以检查缓存状态
    Session = initialize_database()
    session = Session()

    try:
        # 检查当前缓存状态
        cache = get_cached_date_range(session, library_path)
        if cache:
            print(f"📚 当前缓存范围: {cache.start_date} 到 {cache.end_date}")
            print(f"📊 缓存照片数量: {cache.photo_count}")
        else:
            print("📚 当前没有缓存数据")
        print("=" * 80)

        # 模拟连续的查询
        for i in range(3):  # 模拟3次查询
            print(f"\n🔍 第 {i+1} 次查询:")

            # 计算查询的日期范围
            search_end = current_date - timedelta(days=i * days_back)
            search_start = search_end - timedelta(days=days_back)

            start_timestamp = int(search_start.timestamp())
            end_timestamp = int(search_end.timestamp())

            print(f"📅 查询日期范围: {search_start} 到 {search_end}")
            print(f"🔢 时间戳范围: {start_timestamp} 到 {end_timestamp}")

            # 检查这个范围是否已缓存
            is_cached = is_date_range_cached(session, library_path, search_start, search_end)
            print(f"🔍 缓存状态: {'已缓存' if is_cached else '未缓存'}")

            # 执行查询
            try:
                photos = collect_photos_with_smart_cache_by_date_range(
                    library_path=library_path,
                    start_date=start_timestamp,
                    end_date=end_timestamp,
                    max_photos=max_photos_per_batch
                )

                print(f"📸 查询结果: {len(photos)} 张照片")

                if photos:
                    # 显示前几张照片的信息
                    print("📋 前3张照片信息:")
                    for j, photo in enumerate(photos[:3]):
                        if isinstance(photo, dict):
                            filename = photo.get('filename', 'Unknown')
                            date_taken = photo.get('date_taken', 'Unknown')
                        else:
                            filename = photo.filename
                            date_taken = photo.date_taken
                        print(f"  {j+1}. {filename} - {date_taken}")

            except Exception as e:
                print(f"❌ 查询失败: {e}")

            print("-" * 40)

    finally:
        session.close()

def analyze_photo_distribution():
    """分析照片在时间上的分布，找出700+照片的原因"""

    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"

    print("\n📊 分析照片时间分布 - 寻找700+照片的原因")
    print("=" * 80)

    # 查询最近60天的照片分布，每5天一个区间
    current_date = datetime.now(tz=timezone.utc)
    high_density_ranges = []  # 记录高密度的时间范围

    for days_ago in range(0, 61, 5):  # 0, 5, 10, 15, ..., 60
        search_end = current_date - timedelta(days=days_ago)
        search_start = search_end - timedelta(days=5)  # 每次查询5天

        start_timestamp = int(search_start.timestamp())
        end_timestamp = int(search_end.timestamp())

        try:
            photos = collect_photos_with_smart_cache_by_date_range(
                library_path=library_path,
                start_date=start_timestamp,
                end_date=end_timestamp,
                max_photos=0  # 不限制数量，看实际有多少
            )

            photo_count = len(photos)
            status_icon = "🔥" if photo_count > 500 else "📸" if photo_count > 100 else "📷" if photo_count > 0 else "📭"

            print(f"{status_icon} {search_start.strftime('%Y-%m-%d')} 到 {search_end.strftime('%Y-%m-%d')}: {photo_count} 张照片")

            # 记录高密度范围
            if photo_count > 500:
                high_density_ranges.append({
                    'start': search_start,
                    'end': search_end,
                    'count': photo_count,
                    'start_timestamp': start_timestamp,
                    'end_timestamp': end_timestamp
                })

        except Exception as e:
            print(f"❌ 查询 {search_start.strftime('%Y-%m-%d')} 到 {search_end.strftime('%Y-%m-%d')} 失败: {e}")

    # 分析高密度范围
    if high_density_ranges:
        print("\n🔥 发现高密度照片时间范围:")
        print("=" * 80)
        for range_info in high_density_ranges:
            print(f"📅 {range_info['start'].strftime('%Y-%m-%d')} 到 {range_info['end'].strftime('%Y-%m-%d')}: {range_info['count']} 张照片")
            print(f"🔢 时间戳范围: {range_info['start_timestamp']} 到 {range_info['end_timestamp']}")

            # 进一步分析这个范围内的照片分布（按天）
            analyze_daily_distribution(library_path, range_info['start'], range_info['end'])
            print("-" * 40)
    else:
        print("\n✅ 没有发现超过500张照片的5天时间范围")

def analyze_daily_distribution(library_path, start_date, end_date):
    """分析指定范围内每天的照片分布"""
    print(f"📊 详细分析 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的每日分布:")

    current_day = start_date
    while current_day <= end_date:
        day_start = current_day.replace(hour=0, minute=0, second=0, microsecond=0)
        day_end = current_day.replace(hour=23, minute=59, second=59, microsecond=999999)

        start_timestamp = int(day_start.timestamp())
        end_timestamp = int(day_end.timestamp())

        try:
            photos = collect_photos_with_smart_cache_by_date_range(
                library_path=library_path,
                start_date=start_timestamp,
                end_date=end_timestamp,
                max_photos=0
            )

            if len(photos) > 0:
                print(f"  📅 {current_day.strftime('%Y-%m-%d')}: {len(photos)} 张照片")

        except Exception as e:
            print(f"  ❌ {current_day.strftime('%Y-%m-%d')}: 查询失败 - {e}")

        current_day += timedelta(days=1)

if __name__ == "__main__":
    print("🚀 开始调试无限滚动加载问题")

    # 检查Photos库路径是否存在
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"
    if not os.path.exists(library_path):
        print(f"❌ Photos库路径不存在: {library_path}")
        print("请修改脚本中的library_path变量为您的实际Photos库路径")
        sys.exit(1)

    try:
        debug_date_range_queries()
        analyze_photo_distribution()

        print("\n✅ 调试完成")
        print("\n💡 分析结果:")
        print("1. 如果发现某个5天范围内有700+张照片，这说明:")
        print("   - 可能是旅行期间拍摄了大量照片")
        print("   - 可能是连拍或批量导入照片")
        print("   - 可能是某个特殊事件期间的照片")
        print("2. 这种情况下获取700+张照片是正常的，不是bug")
        print("3. 如果需要优化用户体验，可以考虑:")
        print("   - 在前端显示加载进度")
        print("   - 分批渲染照片避免界面卡顿")
        print("   - 提供虚拟滚动优化性能")

    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
