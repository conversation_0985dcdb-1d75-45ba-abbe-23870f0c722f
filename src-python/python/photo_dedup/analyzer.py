"""
Temporal analysis and similarity detection for photo deduplication.
"""

import json
import logging
import os
import time
from datetime import datetime
from typing import Any, Dict, List

import imagehash
from imagededup.methods import AHash, DHash, PHash, WHash
from PIL import Image

from .cache import cache_result
from .database import initialize_database, store_similarity_group
from .models import PhotoInfo, SimilarityGroup

logger = logging.getLogger(__name__)

# 定义并发处理的数量
MAX_WORKERS = 4


class TemporalAnalyzer:
    """Analyzes photos for temporal patterns and similarity."""

    def __init__(self, db_path: str = None):
        self.phash = PHash()
        self.ahash = AHash()
        self.dhash = DHash()
        self.whash = WHash()
        self.db_path = db_path
        self.db_session = None

        # Initialize database if path provided
        if db_path:
            try:
                Session = initialize_database(db_path)
                self.db_session = Session()
            except Exception as e:
                logger.error(f"Database initialization failed: {e}")

    def detect_temporal_groups(self, photos: List[PhotoInfo], time_threshold_seconds: int = 300, similarity_threshold: float = 0.85) -> List[SimilarityGroup]:
        """
        Detect groups of similar photos taken within a time window.

        Args:
            photos: List of photos to analyze
            time_threshold_seconds: Maximum time difference between photos in a group
            similarity_threshold: Minimum similarity score for grouping

        Returns:
            List of SimilarityGroup objects
        """
        if not photos:
            return []

        # Sort photos by date taken
        sorted_photos = sorted(photos, key=lambda p: p.date_taken)

        groups = []
        used_photos = set()

        for i, photo in enumerate(sorted_photos):
            if photo.uuid in used_photos:
                continue

            # Find potential group members
            group_photos = [photo]
            used_photos.add(photo.uuid)

            # Look for similar photos within time window
            for j in range(i + 1, len(sorted_photos)):
                other = sorted_photos[j]

                if other.uuid in used_photos:
                    continue

                # Check time difference
                time_diff = abs((other.date_taken - photo.date_taken).total_seconds())
                if time_diff > time_threshold_seconds:
                    break

                # Check similarity
                similarity = self.calculate_similarity_score(photo, other)
                if similarity >= similarity_threshold:
                    group_photos.append(other)
                    used_photos.add(other.uuid)

            if len(group_photos) > 1:
                group = SimilarityGroup(
                    group_id=f"group_{len(groups)}",
                    photos=group_photos,
                    similarity_score=0.9,  # Default for temporal groups
                    time_range=(min(p.date_taken for p in group_photos), max(p.date_taken for p in group_photos)),
                )
                groups.append(group)

                # Store in database if session is available
                if self.db_session:
                    try:
                        store_similarity_group(self.db_session, group.to_dict())
                    except Exception as e:
                        logger.warning(f"Failed to store similarity group in database: {e}")

        # Close database session
        if self.db_session:
            self.db_session.close()

        return groups

    @cache_result(ttl=7200)  # 缓存2小时
    def calculate_similarity_score(self, photo1: PhotoInfo, photo2: PhotoInfo, use_hash_cache: bool = True) -> float:
        """
        Calculate similarity score between two photos using multiple hash algorithms.

        Args:
            photo1: First photo
            photo2: Second photo
            use_hash_cache: Whether to use cached hash values

        Returns:
            Similarity score between 0.0 and 1.0
        """
        try:
            # Use cached hash values if available
            if use_hash_cache and photo1.hash_values and photo2.hash_values:
                return self._calculate_from_hashes(photo1.hash_values, photo2.hash_values)

            # 优先使用缩略图路径，如果没有则使用原始路径
            path1 = photo1.thumbnail_path or photo1.original_path
            path2 = photo2.thumbnail_path or photo2.original_path

            # Generate hashes from image files (preferably thumbnails)
            return self._calculate_from_images(path1, path2)

        except Exception as e:
            logger.warning(f"Error calculating similarity: {e}")
            return 0.0

    def _calculate_from_hashes(self, hash1: Dict[str, str], hash2: Dict[str, str]) -> float:
        """Calculate similarity from cached hash values with resolution-aware weighting."""
        scores = []

        # 算法权重：基于对分辨率变化的鲁棒性
        # PHash和WHash对分辨率变化最不敏感，给予更高权重
        algorithm_weights = {
            "phash": 0.35,  # 最高权重 - 对分辨率变化最鲁棒
            "whash": 0.30,  # 高权重 - 小波变换对缩放鲁棒
            "ahash": 0.20,  # 中等权重 - 平均hash相对稳定
            "dhash": 0.15,  # 较低权重 - 对像素差异敏感
        }

        weighted_scores = []
        for alg, weight in algorithm_weights.items():
            if alg in hash1 and alg in hash2:
                h1 = imagehash.hex_to_hash(hash1[alg])
                h2 = imagehash.hex_to_hash(hash2[alg])
                hamming_distance = h1 - h2

                # 对于分辨率相关的检测，使用更宽松的阈值
                if alg in ["phash", "whash"]:
                    # PHash和WHash允许更大的汉明距离
                    similarity = max(0.0, 1.0 - hamming_distance / 64.0)
                else:
                    # AHash和DHash使用标准计算
                    similarity = max(0.0, 1.0 - hamming_distance / 64.0)

                weighted_scores.append(similarity * weight)
                scores.append(similarity)

        # 返回加权平均分数，如果没有分数则返回0
        if weighted_scores:
            return sum(weighted_scores)
        elif scores:
            return sum(scores) / len(scores)
        else:
            return 0.0

    def _calculate_from_images(self, path1: str, path2: str) -> float:
        """Calculate similarity by generating hashes from image files."""
        try:
            # Generate perceptual hashes
            with Image.open(path1) as img1, Image.open(path2) as img2:
                hash1_phash = imagehash.phash(img1)
                hash2_phash = imagehash.phash(img2)

                hash1_ahash = imagehash.average_hash(img1)
                hash2_ahash = imagehash.average_hash(img2)

                hash1_dhash = imagehash.dhash(img1)
                hash2_dhash = imagehash.dhash(img2)

                hash1_whash = imagehash.whash(img1)
                hash2_whash = imagehash.whash(img2)

            # Calculate similarities with resolution-aware weighting
            algorithm_weights = {
                "phash": 0.35,  # 最高权重 - 对分辨率变化最鲁棒
                "whash": 0.30,  # 高权重 - 小波变换对缩放鲁棒
                "ahash": 0.20,  # 中等权重 - 平均hash相对稳定
                "dhash": 0.15,  # 较低权重 - 对像素差异敏感
            }

            hash_pairs = [("phash", hash1_phash, hash2_phash), ("whash", hash1_whash, hash2_whash), ("ahash", hash1_ahash, hash2_ahash), ("dhash", hash1_dhash, hash2_dhash)]

            weighted_scores = []
            for alg, h1, h2 in hash_pairs:
                similarity = max(0.0, 1.0 - (h1 - h2) / 64.0)
                weight = algorithm_weights[alg]
                weighted_scores.append(similarity * weight)

            return max(0.0, sum(weighted_scores))

        except Exception as e:
            logger.warning(f"Error generating image hashes: {e}")
            return 0.0

    @cache_result(ttl=7200)  # 缓存2小时
    def generate_hash_values(self, photo: PhotoInfo) -> Dict[str, str]:
        """
        Generate hash values for a photo using multiple algorithms.

        Args:
            photo: Photo to generate hashes for

        Returns:
            Dictionary of hash values by algorithm
        """
        try:
            # 优先使用缩略图路径，如果没有则使用原始路径
            image_path = photo.thumbnail_path or photo.original_path

            if not image_path:
                logger.warning(f"No image path available for {photo.filename}")
                return {}

            if not os.path.exists(image_path):
                logger.warning(f"Image file not found: {image_path}")
                return {}

            with Image.open(image_path) as img:
                # Generate all hash types with performance monitoring
                hash_values = {}
                performance_stats = {}

                # 按预期速度顺序计算（快到慢）
                algorithms = [("ahash", imagehash.average_hash, "AHash"), ("dhash", imagehash.dhash, "DHash"), ("phash", imagehash.phash, "PHash"), ("whash", imagehash.whash, "WHash")]

                total_start = time.time()

                for alg_key, alg_func, alg_name in algorithms:
                    start_time = time.time()
                    hash_value = str(alg_func(img))
                    end_time = time.time()

                    hash_values[alg_key] = hash_value
                    execution_time = (end_time - start_time) * 1000  # 转换为毫秒
                    performance_stats[alg_key] = execution_time

                total_time = (time.time() - total_start) * 1000
                logger.debug(f"Hash计算性能 - 总时间: {total_time:.2f}ms (AHash: {performance_stats['ahash']:.1f}ms, DHash: {performance_stats['dhash']:.1f}ms, PHash: {performance_stats['phash']:.1f}ms, WHash: {performance_stats['whash']:.1f}ms)")

                return hash_values
        except Exception as e:
            logger.warning(f"Error generating hashes for {photo.filename}: {e}")
            return {}


def detect_temporal_groups(photos: List[PhotoInfo], time_threshold_seconds: int = 300, similarity_threshold: float = 0.85) -> List[SimilarityGroup]:
    """
    Convenience function to detect temporal groups.

    Args:
        photos: List of photos to analyze
        time_threshold_seconds: Maximum time difference between photos
        similarity_threshold: Minimum similarity score for grouping

    Returns:
        List of SimilarityGroup objects
    """
    analyzer = TemporalAnalyzer()
    return analyzer.detect_temporal_groups(photos, time_threshold_seconds, similarity_threshold)


def calculate_similarity_score(photo1: PhotoInfo, photo2: PhotoInfo, use_hash_cache: bool = True) -> float:
    """
    Convenience function to calculate similarity score.

    Args:
        photo1: First photo
        photo2: Second photo
        use_hash_cache: Whether to use cached hash values

    Returns:
        Similarity score between 0.0 and 1.0
    """
    analyzer = TemporalAnalyzer()
    return analyzer.calculate_similarity_score(photo1, photo2, use_hash_cache)


def generate_hash_values(photo: PhotoInfo) -> Dict[str, str]:
    """
    Convenience function to generate hash values for a photo.

    Args:
        photo: Photo to generate hashes for

    Returns:
        Dictionary of hash values
    """
    analyzer = TemporalAnalyzer()
    return analyzer.generate_hash_values(photo)


def analyze_photo_collection(photos: List[PhotoInfo], time_threshold_seconds: int = 300, similarity_threshold: float = 0.85) -> Dict[str, Any]:
    """
    Comprehensive analysis of a photo collection.

    Args:
        photos: List of photos to analyze
        time_threshold_seconds: Maximum time difference for grouping
        similarity_threshold: Minimum similarity score for grouping

    Returns:
        Dictionary containing analysis results
    """
    if not photos:
        return {"total_photos": 0, "similarity_groups": [], "duplicate_photos": 0, "analysis_summary": "No photos provided"}

    # Generate hash values for all photos
    photos_with_hashes = []
    for photo in photos:
        if not photo.hash_values:
            photo.hash_values = generate_hash_values(photo)
        photos_with_hashes.append(photo)

    # Detect temporal groups
    groups = detect_temporal_groups(photos_with_hashes, time_threshold_seconds, similarity_threshold)

    # Calculate statistics
    duplicate_photos = sum(len(group.photos) - 1 for group in groups)

    return {"total_photos": len(photos), "similarity_groups": [group.to_dict() for group in groups], "duplicate_photos": duplicate_photos, "analysis_summary": f"Found {len(groups)} similarity groups with {duplicate_photos} potential duplicates"}


def detect_temporal_groups_json(photos_json: str, time_threshold_seconds: int = 300, similarity_threshold: float = 0.85) -> str:
    """
    JSON wrapper for detect_temporal_groups function for Rust integration.

    Args:
        photos_json: JSON string containing list of photo information
        time_threshold_seconds: Maximum time difference between photos in a group
        similarity_threshold: Minimum similarity score for grouping

    Returns:
        JSON string containing list of similarity groups
    """
    try:
        # Parse JSON input
        photos_data = json.loads(photos_json)

        # Convert dictionaries back to PhotoInfo objects
        photos = []
        for photo_dict in photos_data:
            # Convert timestamp back to datetime
            date_taken = datetime.fromtimestamp(photo_dict["date_taken"])

            # Reconstruct location if available
            location = None
            if photo_dict.get("latitude") is not None and photo_dict.get("longitude") is not None:
                location = {"latitude": photo_dict["latitude"], "longitude": photo_dict["longitude"]}

            photo = PhotoInfo(
                uuid=photo_dict["uuid"],
                filename=photo_dict["filename"],
                original_path=photo_dict["original_path"],
                date_taken=date_taken,
                file_size=photo_dict["file_size"],
                width=photo_dict["width"],
                height=photo_dict["height"],
                mime_type=photo_dict["mime_type"],
                thumbnail_path=photo_dict.get("thumbnail_path"),
                hash_values=photo_dict.get("hash_values"),
                camera_model=photo_dict.get("camera_model"),
                location=location,
            )
            photos.append(photo)

        # Create analyzer and detect groups
        analyzer = TemporalAnalyzer()
        groups = analyzer.detect_temporal_groups(photos, time_threshold_seconds, similarity_threshold)

        # Convert groups to dictionaries and return as JSON
        groups_dict = [group.to_dict() for group in groups]
        return json.dumps(groups_dict, ensure_ascii=False, indent=None)

    except Exception as e:
        logger.error(f"Error in detect_temporal_groups_json: {e}")
        # Return empty list as JSON on error
        return json.dumps([])
