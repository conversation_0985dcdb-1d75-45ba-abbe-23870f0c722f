"""
Simple cache implementation for photo data.
"""

import hashlib
import json
import logging
import os
import pickle
import time
from typing import Any, Optional

logger = logging.getLogger(__name__)

class Cache:
    """A simple file-based cache for photo data."""
    
    def __init__(self, cache_dir: str = ".cache"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    def _get_cache_path(self, key: str) -> str:
        """Generate cache file path for a given key."""
        # Create a hash of the key to avoid filesystem issues
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{key_hash}.cache")
        
    def get(self, key: str, ttl: int = 3600) -> Optional[Any]:
        """
        Retrieve value from cache.
        
        Args:
            key: Cache key
            ttl: Time to live in seconds (default: 1 hour)
            
        Returns:
            Cached value or None if not found/expired
        """
        cache_path = self._get_cache_path(key)
        
        if not os.path.exists(cache_path):
            return None
            
        # Check if cache is expired
        if time.time() - os.path.getmtime(cache_path) > ttl:
            try:
                os.remove(cache_path)
            except Exception as e:
                logger.warning(f"Failed to remove expired cache file: {e}")
            return None
            
        try:
            with open(cache_path, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            logger.warning(f"Failed to read cache: {e}")
            return None
            
    def set(self, key: str, value: Any) -> bool:
        """
        Store value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            
        Returns:
            True if successful, False otherwise
        """
        cache_path = self._get_cache_path(key)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(value, f)
            return True
        except Exception as e:
            logger.warning(f"Failed to write cache: {e}")
            return False
            
    def clear(self) -> bool:
        """
        Clear all cache entries.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            for filename in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            return True
        except Exception as e:
            logger.warning(f"Failed to clear cache: {e}")
            return False

# Global cache instance
_global_cache = None

def get_cache() -> Cache:
    """Get global cache instance."""
    global _global_cache
    if _global_cache is None:
        _global_cache = Cache()
    return _global_cache

def cache_result(ttl: int = 3600):
    """
    Decorator to cache function results.
    
    Args:
        ttl: Time to live in seconds
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            key = f"{func.__name__}_{hashlib.md5(pickle.dumps((args, kwargs))).hexdigest()}"
            
            # Try to get from cache
            cache = get_cache()
            result = cache.get(key, ttl)
            
            if result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return result
                
            # Call function and cache result
            logger.debug(f"Cache miss for {func.__name__}")
            result = func(*args, **kwargs)
            cache.set(key, result)
            return result
            
        return wrapper
    return decorator