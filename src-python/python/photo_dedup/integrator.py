"""
整合模块 - 将照片收集、缩略图生成和相似组检测功能整合在一起
"""

import asyncio
import os
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from .collector import collect_photos_from_library
from .thumbnail import generate_thumbnails_batch, ThumbnailConfig
from .analyzer import detect_temporal_groups
from .database import initialize_database, get_or_create_library
from .models import PhotoInfo, ThumbnailInfo, SimilarityGroup

logger = logging.getLogger(__name__)


async def process_photo_library_async(
    library_path: str,
    output_dir: str = "thumbnails",
    db_path: str = "photo_manager.db",
    days_back: int = 30,
    max_photos: int = 1000,
    thumbnail_size: str = "medium",
    thumbnail_quality: int = 85,
    time_threshold_seconds: int = 300,
    similarity_threshold: float = 0.85
) -> Dict[str, Any]:
    """
    异步完整处理照片库：收集照片、生成缩略图、检测相似组
    
    Args:
        library_path: 照片库路径
        output_dir: 缩略图输出目录
        db_path: 数据库路径
        days_back: 收集最近几天的照片
        max_photos: 最大处理照片数量
        thumbnail_size: 缩略图尺寸 (small, medium, large)
        thumbnail_quality: 缩略图质量 (1-100)
        time_threshold_seconds: 时间连续性检测的时间阈值
        similarity_threshold: 相似度阈值
        
    Returns:
        Dict包含处理结果
    """
    try:
        # 初始化数据库
        Session = initialize_database(db_path)
        db_session = Session()
        
        # 获取或创建库记录
        library_name = os.path.basename(library_path)
        library = get_or_create_library(db_session, library_name, library_path, "apple_photos")
        
        # 1. 收集照片
        logger.info(f"开始收集照片: {library_path}")
        photos = await asyncio.get_event_loop().run_in_executor(
            None, 
            collect_photos_from_library,
            library_path,
            days_back,
            max_photos,
            None,  # progress_callback
            db_path
        )
        
        if not photos:
            logger.warning("未收集到任何照片")
            return {
                "status": "completed",
                "photos_collected": 0,
                "thumbnails_generated": 0,
                "similarity_groups": 0,
                "message": "未收集到任何照片"
            }
        
        # 2. 生成缩略图
        logger.info(f"开始生成缩略图: {len(photos)} 张照片")
        thumbnail_config = ThumbnailConfig(
            output_dir=output_dir,
            size=thumbnail_size,
            quality=thumbnail_quality
        )
        
        thumbnails = await asyncio.get_event_loop().run_in_executor(
            None,
            generate_thumbnails_batch,
            photos,
            thumbnail_config,
            None,  # progress_callback
            db_path
        )
        
        # 3. 检测相似组
        logger.info(f"开始检测相似组: {len(photos)} 张照片")
        similarity_groups = await asyncio.get_event_loop().run_in_executor(
            None,
            detect_temporal_groups,
            photos,
            time_threshold_seconds,
            similarity_threshold,
            db_path
        )
        
        # 关闭数据库会话
        db_session.close()
        
        # 返回结果
        result = {
            "status": "completed",
            "photos_collected": len(photos),
            "thumbnails_generated": len(thumbnails),
            "similarity_groups": len(similarity_groups),
            "library_info": {
                "name": library_name,
                "path": library_path,
                "type": "apple_photos"
            },
            "processing_time": datetime.now().isoformat(),
            "message": f"成功处理 {len(photos)} 张照片，生成 {len(thumbnails)} 个缩略图，检测到 {len(similarity_groups)} 个相似组"
        }
        
        logger.info(f"处理完成: {result['message']}")
        return result
        
    except Exception as e:
        logger.error(f"处理照片库时出错: {e}")
        return {
            "status": "error",
            "message": str(e)
        }


def process_photo_library(
    library_path: str,
    output_dir: str = "thumbnails",
    db_path: str = "photo_manager.db",
    days_back: int = 30,
    max_photos: int = 1000,
    thumbnail_size: str = "medium",
    thumbnail_quality: int = 85,
    time_threshold_seconds: int = 300,
    similarity_threshold: float = 0.85
) -> Dict[str, Any]:
    """
    完整处理照片库：收集照片、生成缩略图、检测相似组
    
    Args:
        library_path: 照片库路径
        output_dir: 缩略图输出目录
        db_path: 数据库路径
        days_back: 收集最近几天的照片
        max_photos: 最大处理照片数量
        thumbnail_size: 缩略图尺寸 (small, medium, large)
        thumbnail_quality: 缩略图质量 (1-100)
        time_threshold_seconds: 时间连续性检测的时间阈值
        similarity_threshold: 相似度阈值
        
    Returns:
        Dict包含处理结果
    """
    try:
        # 初始化数据库
        Session = initialize_database(db_path)
        db_session = Session()
        
        # 获取或创建库记录
        library_name = os.path.basename(library_path)
        library = get_or_create_library(db_session, library_name, library_path, "apple_photos")
        
        # 1. 收集照片
        logger.info(f"开始收集照片: {library_path}")
        photos = collect_photos_from_library(
            library_path, 
            days_back, 
            max_photos, 
            db_path=db_path
        )
        
        if not photos:
            logger.warning("未收集到任何照片")
            return {
                "status": "completed",
                "photos_collected": 0,
                "thumbnails_generated": 0,
                "similarity_groups": 0,
                "message": "未收集到任何照片"
            }
        
        # 2. 生成缩略图
        logger.info(f"开始生成缩略图: {len(photos)} 张照片")
        thumbnail_config = ThumbnailConfig(
            output_dir=output_dir,
            size=thumbnail_size,
            quality=thumbnail_quality
        )
        
        thumbnails = generate_thumbnails_batch(
            photos, 
            thumbnail_config, 
            db_path=db_path
        )
        
        # 3. 检测相似组
        logger.info(f"开始检测相似组: {len(photos)} 张照片")
        similarity_groups = detect_temporal_groups(
            photos, 
            time_threshold_seconds, 
            similarity_threshold,
            db_path=db_path
        )
        
        # 关闭数据库会话
        db_session.close()
        
        # 返回结果
        result = {
            "status": "completed",
            "photos_collected": len(photos),
            "thumbnails_generated": len(thumbnails),
            "similarity_groups": len(similarity_groups),
            "library_info": {
                "name": library_name,
                "path": library_path,
                "type": "apple_photos"
            },
            "processing_time": datetime.now().isoformat(),
            "message": f"成功处理 {len(photos)} 张照片，生成 {len(thumbnails)} 个缩略图，检测到 {len(similarity_groups)} 个相似组"
        }
        
        logger.info(f"处理完成: {result['message']}")
        return result
        
    except Exception as e:
        logger.error(f"处理照片库时出错: {e}")
        return {
            "status": "error",
            "message": str(e)
        }


def get_processing_summary(db_path: str = "photo_manager.db") -> Dict[str, Any]:
    """
    获取处理摘要信息
    
    Args:
        db_path: 数据库路径
        
    Returns:
        Dict包含摘要信息
    """
    try:
        # 初始化数据库
        Session = initialize_database(db_path)
        db_session = Session()
        
        # 从数据库获取统计信息
        from sqlalchemy import func
        from .database import Library, Photo, Thumbnail, SimilarityGroup
        
        # 获取库数量
        library_count = db_session.query(func.count(Library.id)).scalar()
        
        # 获取照片数量
        photo_count = db_session.query(func.count(Photo.id)).scalar()
        
        # 获取缩略图数量
        thumbnail_count = db_session.query(func.count(Thumbnail.id)).scalar()
        
        # 获取相似组数量
        group_count = db_session.query(func.count(SimilarityGroup.id)).scalar()
        
        # 关闭数据库会话
        db_session.close()
        
        return {
            "library_count": library_count,
            "photo_count": photo_count,
            "thumbnail_count": thumbnail_count,
            "similarity_group_count": group_count,
            "db_path": db_path
        }
        
    except Exception as e:
        logger.error(f"获取处理摘要时出错: {e}")
        return {
            "status": "error",
            "message": str(e)
        }