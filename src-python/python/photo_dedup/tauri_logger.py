import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict, Optional


class TauriLogFormatter(logging.Formatter):
    """专为Tauri应用优化的日志格式化器"""

    COLORS = {
        "DEBUG": "\033[36m",  # <PERSON>an
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[35m",  # Magenta
    }
    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"

    def format(self, record):
        levelname = record.levelname
        color = self.COLORS.get(levelname, "")
        reset = self.RESET

        if hasattr(record, "tauri_context"):
            context = record.tauri_context
            context_str = f" [{context.get('module', '')}:{context.get('function', '')}]"
        else:
            context_str = f" [{record.module}:{record.funcName}]"

        timestamp = datetime.fromtimestamp(record.created).strftime("%H:%M:%S.%f")[:-3]
        level_display = f"{color}{levelname:8}{reset}"
        message = record.getMessage()
        location = f"{self.DIM}{record.filename}:{record.lineno}{self.RESET}"

        return f"{self.DIM}{timestamp}{self.RESET} {level_display} {message}{context_str} {location}"


class StructuredTauriFormatter(logging.Formatter):
    """结构化日志格式化器，适合Tauri前端解析"""

    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "levelno": record.levelno,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "pathname": record.pathname,
            "process": record.process,
            "thread": record.thread,
            "processName": record.processName,
            "threadName": record.threadName,
        }

        # 添加Tauri特定的上下文信息
        if hasattr(record, "tauri_context"):
            log_entry["tauri_context"] = record.tauri_context

        # 添加进度信息
        if hasattr(record, "progress"):
            log_entry["progress"] = record.progress

        if record.exc_info:
            import traceback

            log_entry["exception"] = {"type": record.exc_info[0].__name__, "message": str(record.exc_info[1]), "traceback": traceback.format_exception(*record.exc_info)}

        return json.dumps(log_entry, ensure_ascii=False, separators=(",", ":"))


class TauriProgressLogger:
    """专为Tauri设计的进度日志记录器"""

    def __init__(self, logger: logging.Logger, total: int, operation: str = "processing"):
        self.logger = logger
        self.total = total
        self.current = 0
        self.operation = operation
        self.start_time = datetime.now()
        self.last_update = self.start_time
        self.update_interval = 1.0  # 最小更新间隔(秒)

    def update(self, current: int = None, message: str = None):
        """更新进度"""
        if current is not None:
            self.current = current
        else:
            self.current += 1

        now = datetime.now()
        if (now - self.last_update).total_seconds() < self.update_interval and self.current < self.total:
            return

        self.last_update = now

        progress = min(self.current / self.total, 1.0) if self.total > 0 else 0.0
        elapsed = (now - self.start_time).total_seconds()
        rate = self.current / elapsed if elapsed > 0 else 0.0
        eta = (self.total - self.current) / rate if rate > 0 else 0

        progress_data = {"operation": self.operation, "current": self.current, "total": self.total, "progress": progress, "percent": f"{progress * 100:.1f}%", "rate": f"{rate:.1f}/s", "elapsed": f"{elapsed:.1f}s", "eta": f"{eta:.1f}s", "message": message or f"Processing {self.current}/{self.total}"}

        self.logger.info(f"Progress: {progress_data['percent']} ({self.current}/{self.total}) - ETA: {progress_data['eta']}", extra={"progress": progress_data, "tauri_context": {"type": "progress"}})


# 全局标志防止重复初始化
_logger_initialized = False
_global_logger = None


class TauriCommandLogger:
    """Tauri命令行日志工具类"""

    def __init__(self, name: str = "tauri_photo_manager"):
        global _logger_initialized, _global_logger
        if _global_logger is None:
            self.logger = self.setup_tauri_logger(name)
            _global_logger = self.logger
        else:
            self.logger = _global_logger
        self.command_context = {}

    def setup_tauri_logger(self, name: str, level: str = "INFO", structured: bool = False, log_file: Optional[str] = None) -> logging.Logger:
        """设置Tauri专用的日志器"""
        global _logger_initialized

        if _logger_initialized:
            return logging.getLogger(name)

        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))

        # 清除所有现有的handlers以防重复
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(getattr(logging, level.upper()))

        if structured:
            handler.setFormatter(StructuredTauriFormatter())
        else:
            handler.setFormatter(TauriLogFormatter())

        logger.addHandler(handler)

        if log_file:
            from pathlib import Path

            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.FileHandler(log_path, encoding="utf-8")
            file_handler.setFormatter(StructuredTauriFormatter())
            logger.addHandler(file_handler)

        # 防止日志传播到根logger
        logger.propagate = False
        _logger_initialized = True
        return logger

    def log_command_start(self, command: str, parameters: Dict[str, Any]):
        """记录命令开始执行"""
        self.command_context = {"command": command, "parameters": parameters, "start_time": datetime.now().isoformat()}
        self.logger.info(f"Starting command: {command}", extra={"tauri_context": {"type": "command_start", "command": command, "parameters": parameters}})

    def log_command_complete(self, command: str, result: Dict[str, Any] = None):
        """记录命令完成"""
        duration = (datetime.now() - datetime.fromisoformat(self.command_context.get("start_time", datetime.now().isoformat()))).total_seconds()
        self.logger.info(f"Command completed: {command} ({duration:.2f}s)", extra={"tauri_context": {"type": "command_complete", "command": command, "duration": duration, "result": result}})

    def log_command_error(self, command: str, error: Exception, context: Dict[str, Any] = None):
        """记录命令错误"""
        self.logger.error(f"Command failed: {command} - {str(error)}", extra={"tauri_context": {"type": "command_error", "command": command, "error_type": type(error).__name__, "error_message": str(error), "context": context or {}}})

    def start_progress(self, operation: str, total: int) -> TauriProgressLogger:
        """开始进度跟踪"""
        return TauriProgressLogger(self.logger, total, operation)

    def log_performance_metrics(self, operation: str, metrics: Dict[str, Any]):
        """记录性能指标"""
        self.logger.info(f"Performance metrics for {operation}", extra={"tauri_context": {"type": "performance_metrics", "operation": operation, "metrics": metrics}})

    def log_library_scan(self, library_path: str, photo_count: int, scan_time: float):
        """记录库扫描结果"""
        self.logger.info(f"Library scan completed: {photo_count} photos from {library_path} ({scan_time:.2f}s)", extra={"tauri_context": {"type": "library_scan_complete", "library_path": library_path, "photo_count": photo_count, "scan_time": scan_time}})

    def log_thumbnail_generation(self, total_photos: int, generated_count: int, skipped_count: int, total_time: float, avg_time_per_photo: float):
        """记录缩略图生成统计"""
        self.logger.info(
            f"Thumbnail generation completed: {generated_count} generated, {skipped_count} skipped from {total_photos} photos ({total_time:.2f}s, avg: {avg_time_per_photo:.3f}s/photo)",
            extra={"tauri_context": {"type": "thumbnail_generation_complete", "total_photos": total_photos, "generated_count": generated_count, "skipped_count": skipped_count, "total_time": total_time, "avg_time_per_photo": avg_time_per_photo}},
        )


# 全局日志实例 - 使用单例模式
def get_tauri_logger():
    """获取全局Tauri日志实例"""
    global _global_logger
    if _global_logger is None:
        return TauriCommandLogger()
    else:
        # 返回已存在的logger实例
        logger_instance = TauriCommandLogger()
        return logger_instance


tauri_logger = get_tauri_logger()
