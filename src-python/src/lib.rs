use pyo3::prelude::*;
use pyo3::wrap_pyfunction;

/// Python模块初始化函数
#[pymodule]
fn photo_dedup(_py: Python, m: &Bound<PyModule>) -> PyResult<()> {
    // 这里将添加Python模块的函数和类
    // 暂时留空，后续添加
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_module_initialization() {
        Python::with_gil(|py| {
            let module = PyModule::new(py, "photo_dedup").unwrap();
            let result = photo_dedup(py, &module);
            assert!(result.is_ok());
        });
    }
}