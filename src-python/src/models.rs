use pyo3::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// Rust version of PhotoInfo for PyO3 integration
#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PyPhotoInfo {
    #[pyo3(get, set)]
    pub uuid: String,
    
    #[pyo3(get, set)]
    pub filename: String,
    
    #[pyo3(get, set)]
    pub original_path: String,
    
    #[pyo3(get, set)]
    pub thumbnail_path: Option<String>,
    
    #[pyo3(get, set)]
    pub date_taken: i64,  // Unix timestamp
    
    #[pyo3(get, set)]
    pub file_size: u64,
    
    #[pyo3(get, set)]
    pub width: u32,
    
    #[pyo3(get, set)]
    pub height: u32,
    
    #[pyo3(get, set)]
    pub mime_type: String,
    
    #[pyo3(get, set)]
    pub camera_model: Option<String>,
    
    #[pyo3(get, set)]
    pub latitude: Option<f64>,
    
    #[pyo3(get, set)]
    pub longitude: Option<f64>,
    
    #[pyo3(get, set)]
    pub hash_values: Option<HashMap<String, String>>,
}

#[pymethods]
impl PyPhotoInfo {
    #[new]
    fn new(
        uuid: String,
        filename: String,
        original_path: String,
        date_taken: i64,
        file_size: u64,
        width: u32,
        height: u32,
        mime_type: String,
    ) -> Self {
        Self {
            uuid,
            filename,
            original_path,
            date_taken,
            file_size,
            width,
            height,
            mime_type,
            thumbnail_path: None,
            camera_model: None,
            latitude: None,
            longitude: None,
            hash_values: None,
        }
    }
}

/// Rust version of ThumbnailConfig for PyO3 integration
#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PyThumbnailConfig {
    #[pyo3(get, set)]
    pub output_dir: String,
    
    #[pyo3(get, set)]
    pub size: String,
    
    #[pyo3(get, set)]
    pub quality: i32,
    
    #[pyo3(get, set)]
    pub max_width: Option<i32>,
    
    #[pyo3(get, set)]
    pub max_height: Option<i32>,
    
    #[pyo3(get, set)]
    pub format: String,
}

#[pymethods]
impl PyThumbnailConfig {
    #[new]
    fn new(output_dir: String) -> Self {
        Self {
            output_dir,
            size: "medium".to_string(),
            quality: 85,
            max_width: None,
            max_height: None,
            format: "JPEG".to_string(),
        }
    }
    
    fn dimensions(&self
    ) -> (i32, i32) {
        match self.size.as_str() {
            "small" => (128, 128),
            "medium" => (256, 256),
            "large" => (512, 512),
            _ => (256, 256),
        }
    }
}

/// Rust version of SimilarityGroup for PyO3 integration
#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PySimilarityGroup {
    #[pyo3(get, set)]
    pub group_id: String,
    
    #[pyo3(get, set)]
    pub photos: Vec<PyPhotoInfo>,
    
    #[pyo3(get, set)]
    pub similarity_score: f32,
    
    #[pyo3(get, set)]
    pub start_time: i64,
    
    #[pyo3(get, set)]
    pub end_time: i64,
}

#[pymethods]
impl PySimilarityGroup {
    #[new]
    fn new(group_id: String, similarity_score: f32, start_time: i64, end_time: i64) -> Self {
        Self {
            group_id,
            photos: Vec::new(),
            similarity_score,
            start_time,
            end_time,
        }
    }
    
    fn add_photo(&mut self,
        photo: PyPhotoInfo
    ) {
        self.photos.push(photo);
    }
    
    fn photo_count(&self
    ) -> usize {
        self.photos.len()
    }
    
    fn duration_seconds(&self
    ) -> f64 {
        (self.end_time - self.start_time) as f64
    }
}

// Conversion utilities
impl From<PyPhotoInfo> for PhotoInfo {
    fn from(py_info: PyPhotoInfo) -> Self {
        PhotoInfo {
            uuid: py_info.uuid,
            filename: py_info.filename,
            original_path: py_info.original_path,
            date_taken: DateTime::from_timestamp(py_info.date_taken, 0)
                .unwrap_or_else(|| DateTime::from_timestamp(0, 0).unwrap()),
            file_size: py_info.file_size,
            width: py_info.width,
            height: py_info.height,
            mime_type: py_info.mime_type,
            thumbnail_path: py_info.thumbnail_path,
            camera_model: py_info.camera_model,
            location: if let (Some(lat), Some(lng)) = (py_info.latitude, py_info.longitude) {
                Some(vec![("latitude".to_string(), lat), ("longitude".to_string(), lng)].into_iter().collect())
            } else {
                None
            },
            hash_values: py_info.hash_values,
        }
    }
}