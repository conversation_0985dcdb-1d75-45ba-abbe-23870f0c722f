{"$message_type":"diagnostic","message":"unused import: `pyo3::wrap_pyfunction`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/lib.rs","byte_start":26,"byte_end":47,"line_start":2,"line_end":2,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use pyo3::wrap_pyfunction;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/lib.rs","byte_start":22,"byte_end":49,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use pyo3::wrap_pyfunction;","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `pyo3::wrap_pyfunction`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/lib.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse pyo3::wrap_pyfunction;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `m`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/lib.rs","byte_start":122,"byte_end":123,"line_start":6,"line_end":6,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"fn photo_dedup(_py: Python, m: &Bound<PyModule>) -> PyResult<()> {","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/lib.rs","byte_start":122,"byte_end":123,"line_start":6,"line_end":6,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"fn photo_dedup(_py: Python, m: &Bound<PyModule>) -> PyResult<()> {","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"_m","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `m`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/lib.rs:6:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn photo_dedup(_py: Python, m: &Bound<PyModule>) -> PyResult<()> {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_m`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"linking with `cc` failed: exit status: 1","code":null,"level":"error","spans":[],"children":[{"message":" \"cc\" \"/var/folders/58/3f9_69ts3bx4slnb8tgl572m0000gn/T/rustcQoNaRR/symbols.o\" \"<22 object files omitted>\" \"<sysroot>/lib/rustlib/aarch64-apple-darwin/lib/{libtest-*,libgetopts-*,libunicode_width-*,librustc_std_workspace_std-*}.rlib\" \"/Users/<USER>/local_doc/l_dev/my/python/photo-deduplication/photo-thumbnail-manager/src-python/target/debug/deps/{libpyo3-8a2f85970f39daa4,libmemoffset-23c6ac3791c4a5fe,libonce_cell-e22bb08fb883a9e7,libpyo3_ffi-f9979b25fcc75095,liblibc-edc730b9504486c1,libserde-b3d5907bda1842ef,libunindent-a55d929b843bbd67}.rlib\" \"<sysroot>/lib/rustlib/aarch64-apple-darwin/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*,libcfg_if-*,liblibc-*,librustc_std_workspace_core-*,liballoc-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-liconv\" \"-lSystem\" \"-lc\" \"-lm\" \"-arch\" \"arm64\" \"-mmacosx-version-min=11.0.0\" \"-o\" \"/Users/<USER>/local_doc/l_dev/my/python/photo-deduplication/photo-thumbnail-manager/src-python/target/debug/deps/photo_dedup-97e733904dd692ff\" \"-Wl,-dead_strip\" \"-nodefaultlibs\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"some arguments are omitted. use `--verbose` to show all linker arguments","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"Undefined symbols for architecture arm64:\n  \"_PyBytes_AsString\", referenced from:\n      pyo3::types::bytes::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..bytes..PyBytes$GT$$GT$::as_bytes::h89c00888ac0c63c2 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyBytes_Size\", referenced from:\n      pyo3::types::bytes::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..bytes..PyBytes$GT$$GT$::as_bytes::h89c00888ac0c63c2 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyErr_GetRaisedException\", referenced from:\n      pyo3::err::err_state::PyErrStateNormalized::take::h03e0ec3112d272d3 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n  \"_PyErr_NewExceptionWithDoc\", referenced from:\n      pyo3::err::PyErr::new_type::hf4f27c5e8aa7b287 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\n  \"_PyErr_Print\", referenced from:\n      pyo3::err::panic_after_error::h8997fd37215acfcf in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\n  \"_PyErr_PrintEx\", referenced from:\n      pyo3::err::PyErr::print_panic_and_unwind::hbb59afa1518686d2 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\n  \"_PyErr_SetObject\", referenced from:\n      pyo3::err::err_state::raise_lazy::hfb8f873b44970d00 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n  \"_PyErr_SetRaisedException\", referenced from:\n      pyo3::err::err_state::PyErrStateInner::restore::h3f4898187b4d9cf0 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n  \"_PyErr_SetString\", referenced from:\n      pyo3::err::err_state::raise_lazy::hfb8f873b44970d00 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n  \"_PyErr_WriteUnraisable\", referenced from:\n      pyo3::err::PyErr::write_unraisable::he5bca00276f7ffe4 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\n  \"_PyEval_RestoreThread\", referenced from:\n      _$LT$pyo3..gil..SuspendGIL$u20$as$u20$core..ops..drop..Drop$GT$::drop::h1ed3743266fe9c13 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n  \"_PyEval_SaveThread\", referenced from:\n      pyo3::gil::prepare_freethreaded_python::_$u7b$$u7b$closure$u7d$$u7d$::hd93ee61382fea741 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n      pyo3::gil::SuspendGIL::new::h2286e66191d829eb in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n  \"_PyExc_BaseException\", referenced from:\n      _$LT$pyo3..exceptions..PyBaseException$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::hac80e72f706632e7 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n      _$LT$pyo3..exceptions..PyBaseException$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::hac80e72f706632e7 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n  \"_PyExc_TypeError\", referenced from:\n      _$LT$pyo3..exceptions..PyTypeError$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::h9f727c9961191320 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n      _$LT$pyo3..exceptions..PyTypeError$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::h9f727c9961191320 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n  \"_PyException_GetTraceback\", referenced from:\n      pyo3::err::err_state::PyErrStateNormalized::ptraceback::h999f5eda038f49d0 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\n  \"_PyGILState_Ensure\", referenced from:\n      pyo3_ffi::pystate::PyGILState_Ensure::he3d10e180e37fcc1 in libpyo3_ffi-f9979b25fcc75095.rlib[3](pyo3_ffi-f9979b25fcc75095.pyo3_ffi.3f1330575dbde8ea-cgu.0.rcgu.o)\n  \"_PyGILState_Release\", referenced from:\n      _$LT$pyo3..gil..GILGuard$u20$as$u20$core..ops..drop..Drop$GT$::drop::h80a04ac7dab21ec7 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n  \"_PyImport_Import\", referenced from:\n      pyo3::types::module::PyModule::import::h747dfa5f87969a19 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\n  \"_PyModule_NewObject\", referenced from:\n      pyo3::types::module::PyModule::new::h432abeb5ff35acec in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\n  \"_PyObject_CallNoArgs\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::call0::h6426fa54e7524cf9 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyObject_GetAttr\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::getattr::inner::hbcde6079e9139d0a in libpyo3-8a2f85970f39daa4.rlib[8](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.05.rcgu.o)\n  \"_PyObject_Repr\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::repr::ha701d324a823e2c3 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyObject_Str\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::str::h532e189fdaa7adb2 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyTraceBack_Print\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$pyo3..types..traceback..PyTraceback$GT$$u20$as$u20$pyo3..types..traceback..PyTracebackMethods$GT$::format::h332c39deacc5fe67 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyType_GetName\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$pyo3..types..typeobject..PyType$GT$$u20$as$u20$pyo3..types..typeobject..PyTypeMethods$GT$::name::ha762584f44951aec in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyType_GetQualName\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$pyo3..types..typeobject..PyType$GT$$u20$as$u20$pyo3..types..typeobject..PyTypeMethods$GT$::qualname::h2dfbcceb5c3a24be in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyUnicode_AsEncodedString\", referenced from:\n      pyo3::types::string::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..string..PyString$GT$$GT$::to_string_lossy::h380463942c688729 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyUnicode_AsUTF8AndSize\", referenced from:\n      pyo3::types::string::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..string..PyString$GT$$GT$::to_str::he4dd4b1f610937f3 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n  \"_PyUnicode_FromStringAndSize\", referenced from:\n      pyo3::types::string::PyString::new::h95f0ff1f7b651355 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n      pyo3::types::string::PyString::intern::h33bd7b8bbcc937e1 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n  \"_PyUnicode_InternInPlace\", referenced from:\n      pyo3::types::string::PyString::intern::h33bd7b8bbcc937e1 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n  \"_Py_InitializeEx\", referenced from:\n      pyo3::gil::prepare_freethreaded_python::_$u7b$$u7b$closure$u7d$$u7d$::hd93ee61382fea741 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n  \"_Py_IsInitialized\", referenced from:\n      pyo3::gil::prepare_freethreaded_python::_$u7b$$u7b$closure$u7d$$u7d$::hd93ee61382fea741 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n      pyo3::gil::GILGuard::acquire::_$u7b$$u7b$closure$u7d$$u7d$::h5f106a8e7b4aa9c2 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\n  \"__Py_Dealloc\", referenced from:\n      _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::h6150cc9754e8ce94 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n      _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::h7085c9d4e30065c6 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n      _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::ha942102a225b92e7 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n      _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::haff4fd07e35f1895 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n      _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::hbd0550dbff7a9f74 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n      _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::hd3d9b212c355b6d4 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n      _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::he4035521d8a81e10 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\n      ...\nld: symbol(s) not found for architecture arm64\nclang: error: linker command failed with exit code 1 (use -v to see invocation)\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: linking with `cc` failed: exit status: 1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m:  \"cc\" \"/var/folders/58/3f9_69ts3bx4slnb8tgl572m0000gn/T/rustcQoNaRR/symbols.o\" \"<22 object files omitted>\" \"<sysroot>/lib/rustlib/aarch64-apple-darwin/lib/{libtest-*,libgetopts-*,libunicode_width-*,librustc_std_workspace_std-*}.rlib\" \"/Users/<USER>/local_doc/l_dev/my/python/photo-deduplication/photo-thumbnail-manager/src-python/target/debug/deps/{libpyo3-8a2f85970f39daa4,libmemoffset-23c6ac3791c4a5fe,libonce_cell-e22bb08fb883a9e7,libpyo3_ffi-f9979b25fcc75095,liblibc-edc730b9504486c1,libserde-b3d5907bda1842ef,libunindent-a55d929b843bbd67}.rlib\" \"<sysroot>/lib/rustlib/aarch64-apple-darwin/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*,libcfg_if-*,liblibc-*,librustc_std_workspace_core-*,liballoc-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-liconv\" \"-lSystem\" \"-lc\" \"-lm\" \"-arch\" \"arm64\" \"-mmacosx-version-min=11.0.0\" \"-o\" \"/Users/<USER>/local_doc/l_dev/my/python/photo-deduplication/photo-thumbnail-manager/src-python/target/debug/deps/photo_dedup-97e733904dd692ff\" \"-Wl,-dead_strip\" \"-nodefaultlibs\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: some arguments are omitted. use `--verbose` to show all linker arguments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: Undefined symbols for architecture arm64:\u001b[0m\n\u001b[0m            \"_PyBytes_AsString\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::bytes::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..bytes..PyBytes$GT$$GT$::as_bytes::h89c00888ac0c63c2 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyBytes_Size\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::bytes::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..bytes..PyBytes$GT$$GT$::as_bytes::h89c00888ac0c63c2 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_GetRaisedException\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::err_state::PyErrStateNormalized::take::h03e0ec3112d272d3 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_NewExceptionWithDoc\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::PyErr::new_type::hf4f27c5e8aa7b287 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_Print\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::panic_after_error::h8997fd37215acfcf in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_PrintEx\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::PyErr::print_panic_and_unwind::hbb59afa1518686d2 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_SetObject\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::err_state::raise_lazy::hfb8f873b44970d00 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_SetRaisedException\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::err_state::PyErrStateInner::restore::h3f4898187b4d9cf0 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_SetString\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::err_state::raise_lazy::hfb8f873b44970d00 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyErr_WriteUnraisable\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::PyErr::write_unraisable::he5bca00276f7ffe4 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyEval_RestoreThread\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..gil..SuspendGIL$u20$as$u20$core..ops..drop..Drop$GT$::drop::h1ed3743266fe9c13 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyEval_SaveThread\", referenced from:\u001b[0m\n\u001b[0m                pyo3::gil::prepare_freethreaded_python::_$u7b$$u7b$closure$u7d$$u7d$::hd93ee61382fea741 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m                pyo3::gil::SuspendGIL::new::h2286e66191d829eb in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyExc_BaseException\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..exceptions..PyBaseException$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::hac80e72f706632e7 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..exceptions..PyBaseException$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::hac80e72f706632e7 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyExc_TypeError\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..exceptions..PyTypeError$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::h9f727c9961191320 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..exceptions..PyTypeError$u20$as$u20$pyo3..type_object..PyTypeInfo$GT$::type_object_raw::_$u7b$$u7b$closure$u7d$$u7d$::h9f727c9961191320 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyException_GetTraceback\", referenced from:\u001b[0m\n\u001b[0m                pyo3::err::err_state::PyErrStateNormalized::ptraceback::h999f5eda038f49d0 in libpyo3-8a2f85970f39daa4.rlib[17](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.14.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyGILState_Ensure\", referenced from:\u001b[0m\n\u001b[0m                pyo3_ffi::pystate::PyGILState_Ensure::he3d10e180e37fcc1 in libpyo3_ffi-f9979b25fcc75095.rlib[3](pyo3_ffi-f9979b25fcc75095.pyo3_ffi.3f1330575dbde8ea-cgu.0.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyGILState_Release\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..gil..GILGuard$u20$as$u20$core..ops..drop..Drop$GT$::drop::h80a04ac7dab21ec7 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyImport_Import\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::module::PyModule::import::h747dfa5f87969a19 in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyModule_NewObject\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::module::PyModule::new::h432abeb5ff35acec in libpyo3-8a2f85970f39daa4.rlib[9](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.06.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyObject_CallNoArgs\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::call0::h6426fa54e7524cf9 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyObject_GetAttr\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::getattr::inner::hbcde6079e9139d0a in libpyo3-8a2f85970f39daa4.rlib[8](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.05.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyObject_Repr\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::repr::ha701d324a823e2c3 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyObject_Str\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$pyo3..types..any..PyAny$GT$$u20$as$u20$pyo3..types..any..PyAnyMethods$GT$::str::h532e189fdaa7adb2 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyTraceBack_Print\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$pyo3..types..traceback..PyTraceback$GT$$u20$as$u20$pyo3..types..traceback..PyTracebackMethods$GT$::format::h332c39deacc5fe67 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyType_GetName\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$pyo3..types..typeobject..PyType$GT$$u20$as$u20$pyo3..types..typeobject..PyTypeMethods$GT$::name::ha762584f44951aec in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyType_GetQualName\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$pyo3..types..typeobject..PyType$GT$$u20$as$u20$pyo3..types..typeobject..PyTypeMethods$GT$::qualname::h2dfbcceb5c3a24be in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyUnicode_AsEncodedString\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::string::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..string..PyString$GT$$GT$::to_string_lossy::h380463942c688729 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyUnicode_AsUTF8AndSize\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::string::_$LT$impl$u20$pyo3..instance..Borrowed$LT$pyo3..types..string..PyString$GT$$GT$::to_str::he4dd4b1f610937f3 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyUnicode_FromStringAndSize\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::string::PyString::new::h95f0ff1f7b651355 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m                pyo3::types::string::PyString::intern::h33bd7b8bbcc937e1 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m            \"_PyUnicode_InternInPlace\", referenced from:\u001b[0m\n\u001b[0m                pyo3::types::string::PyString::intern::h33bd7b8bbcc937e1 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m            \"_Py_InitializeEx\", referenced from:\u001b[0m\n\u001b[0m                pyo3::gil::prepare_freethreaded_python::_$u7b$$u7b$closure$u7d$$u7d$::hd93ee61382fea741 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m            \"_Py_IsInitialized\", referenced from:\u001b[0m\n\u001b[0m                pyo3::gil::prepare_freethreaded_python::_$u7b$$u7b$closure$u7d$$u7d$::hd93ee61382fea741 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m                pyo3::gil::GILGuard::acquire::_$u7b$$u7b$closure$u7d$$u7d$::h5f106a8e7b4aa9c2 in libpyo3-8a2f85970f39daa4.rlib[13](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.10.rcgu.o)\u001b[0m\n\u001b[0m            \"__Py_Dealloc\", referenced from:\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::h6150cc9754e8ce94 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::h7085c9d4e30065c6 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::ha942102a225b92e7 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::haff4fd07e35f1895 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::hbd0550dbff7a9f74 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::hd3d9b212c355b6d4 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m                _$LT$pyo3..instance..Bound$LT$T$GT$$u20$as$u20$core..ops..drop..Drop$GT$::drop::he4035521d8a81e10 in libpyo3-8a2f85970f39daa4.rlib[3](pyo3-8a2f85970f39daa4.pyo3.cb3ab45145f317f-cgu.00.rcgu.o)\u001b[0m\n\u001b[0m                ...\u001b[0m\n\u001b[0m          ld: symbol(s) not found for architecture arm64\u001b[0m\n\u001b[0m          clang: error: linker command failed with exit code 1 (use -v to see invocation)\u001b[0m\n\u001b[0m          \u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 2 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error; 2 warnings emitted\u001b[0m\n\n"}
