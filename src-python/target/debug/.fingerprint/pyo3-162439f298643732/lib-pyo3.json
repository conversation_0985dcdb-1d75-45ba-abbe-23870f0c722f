{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"serde\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-declarative-modules\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"gil-refs\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 11844115677734887287, "profile": 9342662799500328589, "path": 12407359052986208493, "deps": [[629381703529241162, "indoc", false, 10658066816414740504], [2828590642173593838, "cfg_if", false, 8561685339905901311], [2995743780683191069, "build_script_build", false, 11003227792981602818], [3958489542916937055, "portable_atomic", false, 15094405261286003321], [4495526598637097934, "parking_lot", false, 4444994667112306714], [4684437522915235464, "libc", false, 10498577515117410269], [8125750282222427816, "pyo3_ffi", false, 2963472145019484692], [9689903380558560274, "serde", false, 6968589502768590062], [13244445931427159498, "pyo3_macros", false, 14188371994490812735], [14643204177830147187, "memoffset", false, 18188275740972476642], [14748792705540276325, "unindent", false, 5566209048694278901]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-162439f298643732/dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}