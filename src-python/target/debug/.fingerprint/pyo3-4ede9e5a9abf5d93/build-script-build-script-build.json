{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"serde\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"arc_lock\", \"auto-initialize\", \"bigdecimal\", \"chrono\", \"chrono-local\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"jiff-02\", \"lock_api\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"ordered-float\", \"parking_lot\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"time\", \"unindent\", \"uuid\"]", "target": 5408242616063297496, "profile": 11924772593968738075, "path": 3656112822019915256, "deps": [[10288871127199797760, "pyo3_build_config", false, 5243122368824195302]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-4ede9e5a9abf5d93/dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/local/bin/ld64.mold"], "config": 2069994364910194474, "compile_kind": 0}