{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\", \"resolve-config\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"python3-dll-a\", \"resolve-config\"]", "target": 8254743344416261242, "profile": 3033921117576893, "path": 4698546510833404348, "deps": [[3722963349756955755, "once_cell", false, 5439056437246959460], [10296317077653712691, "target_lexicon", false, 8302832563129317005], [17024530325325883827, "build_script_build", false, 7148249446233869297]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-build-config-59289ab0f15b995d/dep-lib-pyo3_build_config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}