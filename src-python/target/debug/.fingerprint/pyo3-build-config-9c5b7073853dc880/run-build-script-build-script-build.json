{"rustc": 6694675083057748860, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10288871127199797760, "build_script_build", false, 16312697744251143245]], "local": [{"RerunIfEnvChanged": {"var": "PYO3_CONFIG_FILE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_NO_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_ENVIRONMENT_SIGNATURE", "val": null}}, {"RerunIfEnvChanged": {"var": "PYO3_PYTHON", "val": null}}, {"RerunIfEnvChanged": {"var": "VIRTUAL_ENV", "val": null}}, {"RerunIfEnvChanged": {"var": "CONDA_PREFIX", "val": null}}, {"RerunIfEnvChanged": {"var": "PATH", "val": "/opt/homebrew/opt/llvm/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/Nextcloud/dev/lib:/opt/homebrew/opt/curl/bin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/opt/local/bin:/opt/local/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin://Applications/Topaz Photo AI.app/Contents/Resources/bin:/Library/Apple/usr/bin:/usr/local/share/dotnet:~/.dotnet/tools:/Users/<USER>/.cargo/bin:/Users/<USER>/.local/bin:/opt/homebrew/opt/binutils/bin:/Users/<USER>/n/bin:/Users/<USER>/.local/bin"}}, {"RerunIfEnvChanged": {"var": "PYO3_USE_ABI3_FORWARD_COMPATIBILITY", "val": null}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/local/bin/ld64.mold"], "config": 0, "compile_kind": 0}