{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\", \"resolve-config\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"python3-dll-a\", \"resolve-config\"]", "target": 8254743344416261242, "profile": 3033921117576893, "path": 14690497410222953441, "deps": [[3346669234123344896, "target_lexicon", false, 7311241766289670765], [3722963349756955755, "once_cell", false, 7058576067141044902], [10288871127199797760, "build_script_build", false, 17504979217608405352]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-build-config-d80369bfe0e3cf23/dep-lib-pyo3_build_config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}