{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\", \"resolve-config\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"python3-dll-a\", \"resolve-config\"]", "target": 8254743344416261242, "profile": 3033921117576893, "path": 14690497410222953441, "deps": [[3346669234123344896, "target_lexicon", false, 14028546951548329325], [3722963349756955755, "once_cell", false, 7405134677086009789], [10288871127199797760, "build_script_build", false, 7063435794991807053]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-build-config-dcbb1b9e9ef6d845/dep-lib-pyo3_build_config", "checksum": false}}], "rustflags": ["-C", "link-arg=-fuse-ld=/usr/local/bin/ld64.mold"], "config": 2069994364910194474, "compile_kind": 0}