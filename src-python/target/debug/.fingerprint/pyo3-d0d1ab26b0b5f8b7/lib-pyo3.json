{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"serde\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 1859062398649441551, "profile": 8678071535421861723, "path": 5686973220387833172, "deps": [[629381703529241162, "indoc", false, 10658066816414740504], [2267156744914913355, "pyo3_ffi", false, 13033852006037787394], [2828590642173593838, "cfg_if", false, 8561685339905901311], [3722963349756955755, "once_cell", false, 10491468581036605219], [4684437522915235464, "libc", false, 10498577515117410269], [9689903380558560274, "serde", false, 6968589502768590062], [10432140972695413122, "build_script_build", false, 4735543113719015593], [14176058866702155369, "pyo3_macros", false, 8763856914210386061], [14643204177830147187, "memoffset", false, 18188275740972476642], [14748792705540276325, "unindent", false, 5566209048694278901]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-d0d1ab26b0b5f8b7/dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}