{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"serde\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"arc_lock\", \"auto-initialize\", \"bigdecimal\", \"chrono\", \"chrono-local\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"jiff-02\", \"lock_api\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"ordered-float\", \"parking_lot\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"time\", \"unindent\", \"uuid\"]", "target": 1859062398649441551, "profile": 12345493230797133551, "path": 10305329622057860153, "deps": [[629381703529241162, "indoc", false, 10658066816414740504], [3722963349756955755, "once_cell", false, 10491468581036605219], [4684437522915235464, "libc", false, 10498577515117410269], [5099523288940447918, "pyo3_ffi", false, 2499514834933165869], [5197680718850464868, "pyo3_macros", false, 16818142729841677080], [9689903380558560274, "serde", false, 6968589502768590062], [9768805234657844767, "build_script_build", false, 12677709157369281937], [14643204177830147187, "memoffset", false, 18188275740972476642], [14748792705540276325, "unindent", false, 5566209048694278901]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ef20328ff360d323/dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}