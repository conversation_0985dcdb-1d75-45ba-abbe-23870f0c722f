{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 12398300617518054423, "profile": 9342662799500328589, "path": 8552426966091684280, "deps": [[4684437522915235464, "libc", false, 10498577515117410269], [8125750282222427816, "build_script_build", false, 16961056436390935068]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ffi-2d574bcb6279a769/dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}