{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 14506753996192664611, "profile": 8678071535421861723, "path": 6449837869007736881, "deps": [[2267156744914913355, "build_script_build", false, 5558004571272285083], [4684437522915235464, "libc", false, 10498577515117410269]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ffi-462c817773b8bd89/dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}