{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 14506753996192664611, "profile": 8678071535421861723, "path": 3606946174236954921, "deps": [[46745629712228035, "build_script_build", false, 12385299695753231203], [4684437522915235464, "libc", false, 10498577515117410269]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ffi-a22615679c903993/dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}