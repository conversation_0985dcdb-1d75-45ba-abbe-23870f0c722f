{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 14506753996192664611, "profile": 12345493230797133551, "path": 13131041331305190756, "deps": [[4684437522915235464, "libc", false, 10498577515117410269], [5099523288940447918, "build_script_build", false, 6461768143925321837]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ffi-c2795f73a02bb18a/dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}