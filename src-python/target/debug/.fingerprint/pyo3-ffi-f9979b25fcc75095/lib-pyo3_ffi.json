{"rustc": 6694675083057748860, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py313\", \"abi3-py314\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 14506753996192664611, "profile": 7887605987709891961, "path": 13131041331305190756, "deps": [[4684437522915235464, "libc", false, 14889154772738619546], [5099523288940447918, "build_script_build", false, 14615576580863061118]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-ffi-f9979b25fcc75095/dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}