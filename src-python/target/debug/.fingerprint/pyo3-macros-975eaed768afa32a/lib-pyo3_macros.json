{"rustc": 6694675083057748860, "features": "[]", "declared_features": "[\"experimental-async\", \"experimental-inspect\", \"multiple-pymethods\"]", "target": 13917622123232857288, "profile": 11924772593968738075, "path": 1377953665686469627, "deps": [[3060637413840920116, "proc_macro2", false, 16416430916144095276], [4342566878770968593, "pyo3_macros_backend", false, 15380357478522428506], [4974441333307933176, "syn", false, 404590910952257687], [17990358020177143287, "quote", false, 8691324552799230375]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/pyo3-macros-975eaed768afa32a/dep-lib-pyo3_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}