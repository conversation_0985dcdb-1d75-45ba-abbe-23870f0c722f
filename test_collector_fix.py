#!/usr/bin/env python3
"""
测试 collector.py 中 cached_photos 变量未定义问题的修复
"""

import sys
import os
import tempfile
from datetime import datetime, timezone, timedelta

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python/python'))

def test_collector_fix():
    """测试修复后的 collect_photos_with_smart_cache_by_date_range 函数"""
    try:
        from photo_dedup.collector import collect_photos_with_smart_cache_by_date_range
        
        # 测试参数
        library_path = '/Users/<USER>/Pictures/Photos Library.photoslibrary'
        end_date = datetime.now(tz=timezone.utc)
        start_date = end_date - timedelta(days=1)
        start_timestamp = int(start_date.timestamp())
        end_timestamp = int(end_date.timestamp())

        print(f'测试时间范围: {start_date} 到 {end_date}')
        print(f'时间戳范围: {start_timestamp} 到 {end_timestamp}')

        # 使用临时数据库
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name

        try:
            # 测试智能缓存收集
            photos = collect_photos_with_smart_cache_by_date_range(
                library_path=library_path,
                start_date=start_timestamp,
                end_date=end_timestamp,
                max_photos=5,
                db_path=db_path
            )
            print(f'✅ 成功收集到 {len(photos)} 张照片')
            for i, photo in enumerate(photos[:3]):
                print(f'  照片 {i+1}: {photo.get("filename", "未知")}')
            return True
        except Exception as e:
            print(f'❌ 测试失败: {e}')
            import traceback
            traceback.print_exc()
            return False
        finally:
            # 清理临时数据库
            if os.path.exists(db_path):
                os.unlink(db_path)
                
    except ImportError as e:
        print(f'❌ 导入失败: {e}')
        return False

if __name__ == '__main__':
    print("开始测试 collector.py 修复...")
    success = test_collector_fix()
    if success:
        print("✅ 测试通过！")
    else:
        print("❌ 测试失败！")
        sys.exit(1)
