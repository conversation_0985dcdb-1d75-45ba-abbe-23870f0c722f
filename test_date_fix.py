#!/usr/bin/env python3
"""
测试日期修复是否正确
"""

import sys
import os
from datetime import datetime, timezone

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

from photo_dedup.collector import collect_photos_with_smart_cache_by_date_range

def test_date_ranges():
    """测试不同的日期范围"""
    
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"
    
    if not os.path.exists(library_path):
        print(f"❌ Photos库路径不存在: {library_path}")
        return
    
    print("🧪 测试日期范围查询")
    print("=" * 60)
    
    # 测试今天
    today = datetime.now(tz=timezone.utc)
    today_start = today.replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    print(f"📅 测试今天: {today_start.strftime('%Y-%m-%d %H:%M:%S')} 到 {today_end.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        photos = collect_photos_with_smart_cache_by_date_range(
            library_path=library_path,
            start_date=int(today_start.timestamp()),
            end_date=int(today_end.timestamp()),
            max_photos=0
        )
        print(f"✅ 今天的照片: {len(photos)} 张")
    except Exception as e:
        print(f"❌ 查询今天失败: {e}")
    
    print("-" * 40)
    
    # 测试昨天
    yesterday = today.replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday = yesterday.replace(day=yesterday.day - 1)
    yesterday_end = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    print(f"📅 测试昨天: {yesterday.strftime('%Y-%m-%d %H:%M:%S')} 到 {yesterday_end.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        photos = collect_photos_with_smart_cache_by_date_range(
            library_path=library_path,
            start_date=int(yesterday.timestamp()),
            end_date=int(yesterday_end.timestamp()),
            max_photos=0
        )
        print(f"✅ 昨天的照片: {len(photos)} 张")
    except Exception as e:
        print(f"❌ 查询昨天失败: {e}")
    
    print("-" * 40)
    
    # 测试最近5天
    five_days_ago = today.replace(day=today.day - 5, hour=0, minute=0, second=0, microsecond=0)
    
    print(f"📅 测试最近5天: {five_days_ago.strftime('%Y-%m-%d %H:%M:%S')} 到 {today_end.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        photos = collect_photos_with_smart_cache_by_date_range(
            library_path=library_path,
            start_date=int(five_days_ago.timestamp()),
            end_date=int(today_end.timestamp()),
            max_photos=0
        )
        print(f"✅ 最近5天的照片: {len(photos)} 张")
        
        if photos:
            print("📋 前3张照片信息:")
            for i, photo in enumerate(photos[:3]):
                if isinstance(photo, dict):
                    filename = photo.get('filename', 'Unknown')
                    date_taken = photo.get('date_taken', 'Unknown')
                else:
                    filename = photo.filename
                    date_taken = photo.date_taken
                
                # 处理日期显示
                if isinstance(date_taken, (int, float)):
                    if date_taken > 1000000000000:  # 毫秒时间戳
                        date_str = datetime.fromtimestamp(date_taken / 1000).strftime('%Y-%m-%d %H:%M:%S')
                    else:  # 秒时间戳
                        date_str = datetime.fromtimestamp(date_taken).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    date_str = str(date_taken)
                
                print(f"  {i+1}. {filename} - {date_str}")
                
    except Exception as e:
        print(f"❌ 查询最近5天失败: {e}")

def test_future_date():
    """测试未来日期（应该返回0张照片）"""
    
    library_path = "/Users/<USER>/Pictures/Photos Library.photoslibrary"
    
    if not os.path.exists(library_path):
        print(f"❌ Photos库路径不存在: {library_path}")
        return
    
    print("\n🔮 测试未来日期查询")
    print("=" * 60)
    
    # 测试明天
    tomorrow = datetime.now(tz=timezone.utc)
    tomorrow = tomorrow.replace(day=tomorrow.day + 1, hour=0, minute=0, second=0, microsecond=0)
    tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    print(f"📅 测试明天: {tomorrow.strftime('%Y-%m-%d %H:%M:%S')} 到 {tomorrow_end.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        photos = collect_photos_with_smart_cache_by_date_range(
            library_path=library_path,
            start_date=int(tomorrow.timestamp()),
            end_date=int(tomorrow_end.timestamp()),
            max_photos=0
        )
        print(f"✅ 明天的照片: {len(photos)} 张 (应该是0)")
        
        if len(photos) > 0:
            print("⚠️ 警告：未来日期查询到了照片，可能存在时区或日期问题")
        
    except Exception as e:
        print(f"❌ 查询明天失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试日期修复")
    
    try:
        test_date_ranges()
        test_future_date()
        
        print(f"\n{'='*60}")
        print("✅ 测试完成")
        print("\n💡 如果看到未来日期的问题:")
        print("1. 检查系统时区设置")
        print("2. 检查Photos库中照片的日期信息")
        print("3. 确认前端日期计算逻辑")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
