#!/usr/bin/env python3
"""
测试 collector.py 语法是否正确
"""

import ast
import sys

def test_syntax():
    """测试 collector.py 文件的语法"""
    try:
        with open('src-python/python/photo_dedup/collector.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 尝试解析语法
        ast.parse(source_code)
        print("✅ collector.py 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == '__main__':
    print("开始语法检查...")
    success = test_syntax()
    if success:
        print("✅ 语法检查通过！")
    else:
        print("❌ 语法检查失败！")
        sys.exit(1)
